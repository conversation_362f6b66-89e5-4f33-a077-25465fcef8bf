﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - 社群登入示範</title>
    <!-- Bootstrap CSS from CDN (載入在前) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- 自定義 CSS (載入在後，可以覆蓋 Bootstrap) -->
    @Styles.Render("~/Content/css")

    <!-- 強制社交登入按鈕樣式 -->
    <style>
        .btn.btn-facebook,
        button.btn-facebook,
        a.btn-facebook {
            background: linear-gradient(135deg, #3b5998 0%, #4c70ba 100%) !important;
            border: none !important;
            color: white !important;
            text-decoration: none !important;
            padding: 12px 24px !important;
            font-size: 16px !important;
            border-radius: 8px !important;
        }

        .btn.btn-facebook:hover,
        .btn.btn-facebook:focus,
        .btn.btn-facebook:active,
        button.btn-facebook:hover,
        a.btn-facebook:hover {
            background: linear-gradient(135deg, #2d4373 0%, #3b5998 100%) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .btn.btn-google,
        button.btn-google,
        a.btn-google {
            background: linear-gradient(135deg, #dd4b39 0%, #ea4335 100%) !important;
            border: none !important;
            color: white !important;
            text-decoration: none !important;
            padding: 12px 24px !important;
            font-size: 16px !important;
            border-radius: 8px !important;
        }

        .btn.btn-google:hover,
        .btn.btn-google:focus,
        .btn.btn-google:active,
        button.btn-google:hover,
        a.btn-google:hover {
            background: linear-gradient(135deg, #c23321 0%, #dd4b39 100%) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .btn.btn-line,
        button.btn-line,
        a.btn-line {
            background: linear-gradient(135deg, #00c300 0%, #00b900 100%) !important;
            border: none !important;
            color: white !important;
            text-decoration: none !important;
            padding: 12px 24px !important;
            font-size: 16px !important;
            border-radius: 8px !important;
        }

        .btn.btn-line:hover,
        .btn.btn-line:focus,
        .btn.btn-line:active,
        button.btn-line:hover,
        a.btn-line:hover {
            background: linear-gradient(135deg, #009900 0%, #00c300 100%) !important;
            color: white !important;
            text-decoration: none !important;
        }

        /* 圖示 */
        .btn-facebook:before { content: "📘 "; margin-right: 8px; }
        .btn-google:before { content: "🔍 "; margin-right: 8px; }
        .btn-line:before { content: "💬 "; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="navbar navbar-inverse navbar-fixed-top">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                @Html.ActionLink("🌟 社群登入示範", "Index", "Home", new { area = "" }, new { @class = "navbar-brand" })
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav">
                    <li>@Html.ActionLink("🏠 首頁", "Index", "Home")</li>
                    <li>@Html.ActionLink("🔐 登入", "Login", "Account")</li>
                    <li>@Html.ActionLink("👤 個人資料", "Profile", "Account")</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="container body-content">
        @RenderBody()
    </div>

    <footer>
        <div class="container">
            <p>&copy; @DateTime.Now.Year - 社群登入示範 | 支援 Facebook、Google、LINE 登入</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS from CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    @RenderSection("scripts", required: false)

    <script>
        // 確保響應式導航選單正常工作
        $(document).ready(function() {
            // 檢查 jQuery 和 Bootstrap 是否正確載入
            if (typeof jQuery === 'undefined') {
                console.error('jQuery 未正確載入');
                return;
            }

            if (typeof $.fn.collapse === 'undefined') {
                console.warn('Bootstrap JavaScript 未正確載入');
                return;
            }

            console.log('✅ jQuery 和 Bootstrap 已正確載入');

            // Bootstrap 的響應式導航應該自動工作
            // 這裡不需要額外的程式碼，Bootstrap 會自動處理
        });
    </script>
</body>
</html>