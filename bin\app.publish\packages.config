﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="*******" targetFramework="net481" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net481" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net481" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net481" />
  <package id="Microsoft.Owin" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Owin.Security" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Owin.Security.Facebook" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Owin.Security.Google" version="4.2.3" targetFramework="net481" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.1" targetFramework="net481" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net481" />
  <package id="Owin" version="1.0.0" targetFramework="net481" />
  <package id="WebGrease" version="1.6.0" targetFramework="net481" />
</packages>