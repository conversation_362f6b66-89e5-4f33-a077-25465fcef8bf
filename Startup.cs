/*
 * 社群登入示範系統 - OWIN Startup Configuration
 * Copyright © 2025 芯禾數位行銷 (Singho Digital Marketing). All rights reserved.
 *
 * 此檔案包含 OWIN 中介軟體的設定，包括 Cookie 驗證和社群登入提供者設定
 */

using System;
using System.Collections.Generic;
using System.Configuration;
using Microsoft.Owin;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.Google;
using Microsoft.Owin.Security.Facebook;
using Owin;

[assembly: OwinStartup(typeof(SocialLoginMvc.Startup))]



namespace SocialLoginMvc
{
    // 社交登入設定類別 - 從 Web.config 讀取
    public static class SocialAuthConfig
    {
        // Facebook 設定
        public static string FacebookAppId => ConfigurationManager.AppSettings["FacebookAppId"];
        public static string FacebookAppSecret => ConfigurationManager.AppSettings["FacebookAppSecret"];

        // Google 設定
        public static string GoogleClientId => ConfigurationManager.AppSettings["GoogleClientId"];
        public static string GoogleClientSecret => ConfigurationManager.AppSettings["GoogleClientSecret"];

        // LINE 設定
        public static string LineChannelId => ConfigurationManager.AppSettings["LineChannelId"];
        public static string LineChannelSecret => ConfigurationManager.AppSettings["LineChannelSecret"];
    }

    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            ConfigureAuth(app);
        }

        public void ConfigureAuth(IAppBuilder app)
        {
            // 處理反向代理的 HTTPS 標頭
            app.Use(async (context, next) =>
            {
                // 檢查反向代理的 HTTPS 標頭
                var forwardedProto = context.Request.Headers.Get("X-Forwarded-Proto");
                var forwardedFor = context.Request.Headers.Get("X-Forwarded-For");

                if (!string.IsNullOrEmpty(forwardedProto) && forwardedProto.Equals("https", StringComparison.OrdinalIgnoreCase))
                {
                    context.Request.Scheme = "https";
                }

                await next();
            });

            // 啟用 Cookie 身份驗證（必須在外部身份驗證之前）
            app.UseCookieAuthentication(new CookieAuthenticationOptions
            {
                AuthenticationType = "ApplicationCookie",
                LoginPath = new PathString("/Account/Login"),
                ExpireTimeSpan = System.TimeSpan.FromMinutes(30),
                SlidingExpiration = true,
                CookieSecure = CookieSecureOption.SameAsRequest // 根據請求協議設定安全性
            });

            // 設定預設的登入類型，讓外部身份驗證中介軟體知道要使用哪個類型
            app.Properties["Microsoft.Owin.Security.Constants.DefaultSignInAsAuthenticationType"] = "ApplicationCookie";

            // 啟用 Google 身份驗證 - 從 Web.config 讀取憑證
            app.UseGoogleAuthentication(new GoogleOAuth2AuthenticationOptions()
            {
                ClientId = SocialAuthConfig.GoogleClientId,
                ClientSecret = SocialAuthConfig.GoogleClientSecret,
                CallbackPath = new PathString("/signin-google"),
                AuthenticationType = "Google", // 明確設定身份驗證類型
                SignInAsAuthenticationType = "ApplicationCookie", // 關鍵設定：讓 OWIN 自動登入
                Scope = { "openid", "profile", "email" }, // 添加 profile scope 來獲取頭像
                Provider = new Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider()
                {
                    OnAuthenticated = async (context) =>
                    {
                        // 使用 Access Token 調用 Google UserInfo API 獲取頭像
                        var pictureUrl = "https://placehold.co/150x150?text=Google+User";

                        try
                        {
                            using (var httpClient = new System.Net.Http.HttpClient())
                            {
                                var userInfoUrl = $"https://www.googleapis.com/oauth2/v2/userinfo?access_token={context.AccessToken}";
                                var response = await httpClient.GetAsync(userInfoUrl);

                                if (response.IsSuccessStatusCode)
                                {
                                    var json = await response.Content.ReadAsStringAsync();
                                    var userInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(json);

                                    if (userInfo?.picture != null)
                                    {
                                        pictureUrl = userInfo.picture.ToString();
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 如果獲取失敗，使用預設頭像
                        }

                        context.Identity.AddClaim(new System.Security.Claims.Claim("picture", pictureUrl));
                    }
                }
            });

            // 啟用 Facebook 身份驗證 - 從 Web.config 讀取憑證
            app.UseFacebookAuthentication(new FacebookAuthenticationOptions()
            {
                AppId = SocialAuthConfig.FacebookAppId,
                AppSecret = SocialAuthConfig.FacebookAppSecret,
                CallbackPath = new PathString("/signin-facebook"),
                AuthenticationType = "Facebook", // 明確設定身份驗證類型
                SignInAsAuthenticationType = "ApplicationCookie", // 關鍵設定：讓 OWIN 自動登入
                Scope = { "email", "public_profile" },
                Provider = new FacebookAuthenticationProvider()
                {
                    OnAuthenticated = (context) =>
                    {
                        // 添加 Facebook 頭像 URL 到 Claims
                        if (!string.IsNullOrEmpty(context.Id))
                        {
                            // Facebook 頭像 URL 格式
                            var pictureUrl = $"https://graph.facebook.com/{context.Id}/picture?type=large";
                            context.Identity.AddClaim(new System.Security.Claims.Claim("picture", pictureUrl));
                        }

                        return System.Threading.Tasks.Task.FromResult<object>(null);
                    }
                }
            });
        }
    }
}
