/*
 * 社群登入示範系統 - OAuth Models
 * Copyright © 2025 芯禾數位行銷 (Singho Digital Marketing). All rights reserved.
 *
 * 此檔案包含 OAuth 相關的通用資料模型和 ActionResult 定義
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Microsoft.Owin.Security;

namespace SocialLoginMvc.Models
{
    /// <summary>
    /// 自定義 OAuth Challenge Result
    /// 用於重定向到外部身份驗證提供者
    /// </summary>
    public class ChallengeResult : ActionResult
    {
        /// <summary>
        /// 登入提供者名稱 (Facebook, Google, LINE 等)
        /// </summary>
        public string LoginProvider { get; set; }

        /// <summary>
        /// 重定向 URI
        /// </summary>
        public string RedirectUri { get; set; }

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="provider">登入提供者</param>
        /// <param name="redirectUri">重定向 URI</param>
        public ChallengeResult(string provider, string redirectUri)
        {
            LoginProvider = provider;
            RedirectUri = redirectUri;
        }

        /// <summary>
        /// 執行 Challenge 結果
        /// </summary>
        /// <param name="context">Controller 上下文</param>
        public override void ExecuteResult(ControllerContext context)
        {
            var properties = new AuthenticationProperties { RedirectUri = RedirectUri };
            context.HttpContext.GetOwinContext().Authentication.Challenge(properties, LoginProvider);
        }
    }
}
