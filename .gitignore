# Visual Studio build outputs
bin/
obj/

# NuGet packages
packages/
*.nupkg

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio cache files
*.cache

# Publish profiles and publish output
PublishProfiles/
*.pubxml
*.pubxml.user
publish/
*.Publish.xml

# Web publish output
*.[Pp]ublish.xml
*.azurePubxml

# NuGet Packages Directory
packages/

# Windows Azure Build Output
csx/
*.build.csdef

# Windows Store app package directory
AppPackages/

# Others
*.Cache
ClientBin/
[Ss]tyle[Cc]op.*
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.pfx
*.publishsettings
node_modules/

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file to a newer
# Visual Studio version. Backup files are not needed, because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm

# SQL Server files
*.mdf
*.ldf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings

# Microsoft Fakes
FakesAssemblies/

# Temporary files
*.tmp
*.temp

# Deploy packages
*.zip
