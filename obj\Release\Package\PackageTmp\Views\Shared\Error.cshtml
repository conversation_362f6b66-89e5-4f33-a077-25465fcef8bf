@model System.Web.Mvc.HandleErrorInfo

@{
    ViewBag.Title = "Error";
}

<h1 class="text-danger">Error.</h1>
<h2 class="text-danger">An error occurred while processing your request.</h2>

@if (Model != null)
{
    <p>
        <strong>Controller:</strong> @Model.ControllerName<br />
        <strong>Action:</strong> @Model.ActionName<br />
        <strong>Exception:</strong> @Model.Exception.Message
    </p>
}
