/*
 * 社群登入示範系統 - LINE OAuth Models
 * Copyright © 2025 芯禾數位行銷 (Singho Digital Marketing). All rights reserved.
 *
 * 此檔案包含 LINE OAuth 相關的資料模型定義
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SocialLoginMvc.Models
{
    /// <summary>
    /// LINE OAuth Token 回應模型
    /// </summary>
    public class LineTokenResponse
    {
        [Newtonsoft.Json.JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [Newtonsoft.Json.JsonProperty("token_type")]
        public string TokenType { get; set; }

        [Newtonsoft.Json.JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [Newtonsoft.Json.JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        [Newtonsoft.Json.JsonProperty("scope")]
        public string Scope { get; set; }

        [Newtonsoft.Json.JsonProperty("id_token")]
        public string IdToken { get; set; }
    }

    /// <summary>
    /// LINE 用戶資訊模型
    /// </summary>
    public class LineUserInfo
    {
        /// <summary>
        /// LINE User ID
        /// </summary>
        public string Sub { get; set; }

        /// <summary>
        /// 顯示名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 個人頭像 URL
        /// </summary>
        public string Picture { get; set; }
    }
}
