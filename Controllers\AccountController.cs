/*
 * 社群登入示範系統 - Account Controller
 * Copyright © 2025 芯禾數位行銷 (Singho Digital Marketing). All rights reserved.
 *
 * 此檔案包含社群登入功能的主要控制邏輯，支援 Facebook、Google、LINE 等平台
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using Microsoft.Owin.Security;
using SocialLoginMvc.Models;



namespace SocialLoginMvc.Controllers
{
    public class AccountController : Controller
    {
        private IAuthenticationManager AuthenticationManager
        {
            get
            {
                return HttpContext.GetOwinContext().Authentication;
            }
        }

        // GET: Account/Login
        public ActionResult Login()
        {
            return View();
        }

        // POST: Account/GoogleLogin
        [HttpPost]
        public ActionResult GoogleLogin()
        {
            // 使用 OWIN 的 Challenge 方法來啟動 Google 身份驗證
            return new ChallengeResult("Google", Url.Action("GoogleCallback", "Account"));
        }

        // POST: Account/LineLogin - LINE 登入
        [HttpPost]
        public ActionResult LineLogin()
        {
            // LINE Login 參數 - 從 Web.config 讀取
            var clientId = SocialLoginMvc.SocialAuthConfig.LineChannelId;

            // 建構基礎 URL - 對於反向代理環境，強制使用 HTTPS
            var baseUrl = Request.Url.GetLeftPart(UriPartial.Authority);
            if (baseUrl.StartsWith("http://"))
            {
                baseUrl = baseUrl.Replace("http://", "https://");
            }

            var redirectUri = baseUrl + Request.ApplicationPath.TrimEnd('/') + "/Account/LineCallback";
            var state = Guid.NewGuid().ToString();
            var nonce = Guid.NewGuid().ToString();

            // 儲存 state 和 nonce 以防止 CSRF 攻擊
            Session["LineOAuthState"] = state;
            Session["LineOAuthNonce"] = nonce;

            // 建構 LINE OAuth URL
            var lineAuthUrl = $"https://access.line.me/oauth2/v2.1/authorize?" +
                            $"response_type=code&" +
                            $"client_id={clientId}&" +
                            $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                            $"state={state}&" +
                            $"scope=profile%20openid&" +
                            $"nonce={nonce}";

            return Redirect(lineAuthUrl);
        }

        // 檢查是否透過 HTTPS 訪問（包含反向代理）
        private bool IsHttpsRequest()
        {
            // 檢查直接 HTTPS 連線
            if (Request.IsSecureConnection)
                return true;

            // 檢查反向代理的 HTTPS 標頭
            var forwardedProto = Request.Headers["X-Forwarded-Proto"];
            if (!string.IsNullOrEmpty(forwardedProto) && forwardedProto.Equals("https", StringComparison.OrdinalIgnoreCase))
                return true;

            // 檢查其他常見的 HTTPS 標頭
            var forwardedSsl = Request.Headers["X-Forwarded-SSL"];
            if (!string.IsNullOrEmpty(forwardedSsl) && forwardedSsl.Equals("on", StringComparison.OrdinalIgnoreCase))
                return true;

            return false;
        }

        // GET/POST: Account/FacebookLogin - 使用 OWIN
        public ActionResult FacebookLogin()
        {
            // 對於反向代理環境，不進行 HTTPS 重定向檢查
            // 因為內部是 HTTP，但外部是 HTTPS

            // 使用 OWIN 的 Challenge 方法來啟動 Facebook 身份驗證
            return new ChallengeResult("Facebook", Url.Action("FacebookCallback", "Account"));
        }

        // GET: Account/GoogleCallback
        public ActionResult GoogleCallback(string error, string error_description, string error_reason, string code, string state)
        {
            // 檢查是否有錯誤參數
            if (!string.IsNullOrEmpty(error))
            {
                string errorMessage;
                switch (error)
                {
                    case "access_denied":
                        if (!string.IsNullOrEmpty(code))
                        {
                            // 有授權碼但還是 access_denied，可能是應用程式設定問題
                            errorMessage = $"Google 登入被拒絕，但已收到授權碼。可能的原因：\n" +
                                         $"1. Google OAuth 應用程式設定問題\n" +
                                         $"2. 權限範圍設定錯誤\n" +
                                         $"3. 應用程式狀態問題\n\n" +
                                         $"請檢查 Google Cloud Console 中的 OAuth 設定。";
                        }
                        else
                        {
                            errorMessage = "您在 Google 授權頁面拒絕了應用程式的權限請求。如要使用 Google 登入，請重新嘗試並點擊「允許」。";
                        }
                        break;
                    case "server_error":
                        errorMessage = "Google 伺服器發生錯誤，請稍後再試。";
                        break;
                    case "temporarily_unavailable":
                        errorMessage = "Google 服務暫時無法使用，請稍後再試。";
                        break;
                    default:
                        errorMessage = $"Google 登入發生錯誤: {error}" +
                                     (!string.IsNullOrEmpty(error_description) ? $" - {error_description}" : "") +
                                     (!string.IsNullOrEmpty(error_reason) ? $" (原因: {error_reason})" : "");
                        break;
                }

                ViewBag.Error = errorMessage;
                return View("Login");
            }

            return ProcessExternalCallback("Google", "Google 用戶");
        }

        // GET: Account/FacebookCallback
        public ActionResult FacebookCallback(string error, string error_description, string error_reason, string code, string state)
        {
            // 檢查是否有錯誤參數
            if (!string.IsNullOrEmpty(error))
            {
                string errorMessage;
                switch (error)
                {
                    case "access_denied":
                        if (error_reason == "user_denied")
                        {
                            errorMessage = "您在 Facebook 授權頁面拒絕了應用程式的權限請求。如要使用 Facebook 登入，請重新嘗試並點擊「繼續」。";
                        }
                        else
                        {
                            errorMessage = $"Facebook 登入被拒絕。可能的原因：\n" +
                                         $"1. Facebook 應用程式設定問題\n" +
                                         $"2. 重定向 URI 不匹配\n" +
                                         $"3. 應用程式權限設定錯誤\n\n" +
                                         $"錯誤詳情: {error_reason} - {error_description}";
                        }
                        break;
                    case "server_error":
                        errorMessage = "Facebook 伺服器發生錯誤，請稍後再試。";
                        break;
                    case "temporarily_unavailable":
                        errorMessage = "Facebook 服務暫時無法使用，請稍後再試。";
                        break;
                    default:
                        errorMessage = $"Facebook 登入發生錯誤: {error}" +
                                     (!string.IsNullOrEmpty(error_description) ? $" - {error_description}" : "") +
                                     (!string.IsNullOrEmpty(error_reason) ? $" (原因: {error_reason})" : "");
                        break;
                }

                ViewBag.Error = errorMessage;
                return View("Login");
            }

            // Facebook 的正確身份驗證類型名稱
            return ProcessExternalCallback("Facebook", "Facebook 用戶");
        }
        // GET: Account/LineCallback - LINE 回調處理
        public async Task<ActionResult> LineCallback(string code, string state, string error)
        {
            try
            {


                // 檢查錯誤
                if (!string.IsNullOrEmpty(error))
                {
                    ViewBag.ErrorMessage = $"LINE 授權失敗：{error}";
                    return View("Login");
                }

                // 驗證 state 參數
                var sessionState = Session["LineOAuthState"] as string;

                if (string.IsNullOrEmpty(state) || state != sessionState)
                {
                    ViewBag.ErrorMessage = $"無效的 state 參數。Session: {sessionState}, Received: {state}";
                    return View("Login");
                }

                // 清除 session state
                Session.Remove("LineOAuthState");
                var nonce = Session["LineOAuthNonce"] as string;
                Session.Remove("LineOAuthNonce");

                if (string.IsNullOrEmpty(code))
                {
                    ViewBag.ErrorMessage = "未收到授權碼";
                    return View("Login");
                }

                // 使用授權碼換取存取權杖
                var tokenResponse = await ExchangeLineCodeForTokenAsync(code);
                if (tokenResponse == null)
                {
                    ViewBag.ErrorMessage = "無法獲取 LINE 存取權杖";
                    return View("Login");
                }

                // 驗證 ID Token 並獲取用戶資訊
                var userInfo = await GetLineUserInfoAsync(tokenResponse.IdToken, nonce);
                if (userInfo == null)
                {
                    ViewBag.ErrorMessage = "無法獲取 LINE 用戶資訊";
                    return View("Login");
                }

                // 創建用戶身份
                var identity = new ClaimsIdentity("ApplicationCookie");
                identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, userInfo.Sub));
                identity.AddClaim(new Claim(ClaimTypes.Name, userInfo.Name));
                identity.AddClaim(new Claim("LoginProvider", "LINE"));
                identity.AddClaim(new Claim("LoginTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));

                if (!string.IsNullOrEmpty(userInfo.Picture))
                {
                    identity.AddClaim(new Claim("Picture", userInfo.Picture));
                }

                // 登入用戶
                var authProperties = new Microsoft.Owin.Security.AuthenticationProperties
                {
                    IsPersistent = false,
                    ExpiresUtc = DateTime.UtcNow.AddHours(8)
                };

                AuthenticationManager.SignIn(authProperties, identity);

                return RedirectToAction("Profile", "Account");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LineCallback 錯誤: {ex.Message}");
                ViewBag.ErrorMessage = $"LINE 登入失敗：{ex.Message}";
                return View("Login");
            }
        }





        // 處理外部身份驗證回調的共用方法
        private ActionResult ProcessExternalCallback(string provider, string defaultUserName)
        {
            // 檢查 OWIN 是否已自動登入用戶（因為有 SignInAsAuthenticationType = "ApplicationCookie"）
            if (User.Identity.IsAuthenticated)
            {
                // 用戶已被 OWIN 自動登入，直接重定向到 Profile 頁面
                TempData["SuccessMessage"] = $"✅ {provider} 登入成功！歡迎 {User.Identity.Name}";
                return RedirectToAction("Profile", "Account");
            }

            // 如果到這裡，表示 OWIN 沒有自動登入用戶，這是意外情況
            // 嘗試手動獲取身份驗證結果作為備用方案
            try
            {
                var result = AuthenticationManager.AuthenticateAsync(provider).Result;

                if (result == null)
                {
                    ViewBag.Error = $"登入失敗：無法從 {provider} 獲取身份驗證資訊。";
                    return View("Login");
                }

                if (result.Identity == null || !result.Identity.Claims.Any())
                {
                    ViewBag.Error = $"登入失敗：無法從 {provider} 獲取用戶資訊。";
                    return View("Login");
                }

                // 手動處理登入（備用方案）
                var identity = new ClaimsIdentity("ApplicationCookie");

                // 從外部身份中提取 Claims
                foreach (var claim in result.Identity.Claims)
                {
                    switch (claim.Type)
                    {
                        case ClaimTypes.NameIdentifier:
                            identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, claim.Value));
                            break;
                    case ClaimTypes.Name:
                        identity.AddClaim(new Claim(ClaimTypes.Name, claim.Value));
                        break;
                    case ClaimTypes.Email:
                        identity.AddClaim(new Claim(ClaimTypes.Email, claim.Value));
                        break;
                    case "picture":
                        identity.AddClaim(new Claim("picture", claim.Value));
                        break;
                }
            }

            // 添加自定義 Claims
            identity.AddClaim(new Claim("LoginProvider", $"{provider} (Microsoft OWIN)"));
            identity.AddClaim(new Claim("LoginTime", DateTime.Now.ToString()));

            // 如果沒有名稱，使用預設值
            if (!identity.Claims.Any(c => c.Type == ClaimTypes.Name))
            {
                identity.AddClaim(new Claim(ClaimTypes.Name, defaultUserName));
            }

            // 登入用戶
            AuthenticationManager.SignIn(new AuthenticationProperties { IsPersistent = false }, identity);

                // 添加成功訊息到 TempData，這樣重定向後還能顯示
                TempData["SuccessMessage"] = $"✅ {provider} 登入成功！歡迎 {identity.FindFirst(ClaimTypes.Name)?.Value ?? defaultUserName}";

                return RedirectToAction("Profile", "Account");
            }
            catch (Exception ex)
            {
                ViewBag.Error = $"登入過程中發生錯誤: {ex.Message}";
                return View("Login");
            }
        }

        // GET: Account/Profile
        public new ActionResult Profile()
        {
            if (!User.Identity.IsAuthenticated)
            {
                ViewBag.UserName = "尚未登入";
                ViewBag.Email = "尚未登入";
                ViewBag.UserPicture = "https://placehold.co/150x150?text=Not+Logged+In";
                ViewBag.LoginProvider = "尚未登入";
                ViewBag.LoginTime = "尚未登入";
                ViewBag.IsAuthenticated = false;
            }
            else
            {
                var claimsIdentity = User.Identity as ClaimsIdentity;

                ViewBag.UserName = claimsIdentity?.FindFirst(ClaimTypes.Name)?.Value ?? "未知用戶";
                ViewBag.Email = claimsIdentity?.FindFirst(ClaimTypes.Email)?.Value ?? "未提供";

                // 改進的頭像獲取邏輯
                var picture = claimsIdentity?.FindFirst("picture")?.Value;
                var email = claimsIdentity?.FindFirst(ClaimTypes.Email)?.Value;
                var loginProvider = claimsIdentity?.FindFirst("LoginProvider")?.Value ?? "未知";

                if (!string.IsNullOrEmpty(picture))
                {
                    ViewBag.UserPicture = picture;
                }
                else if (!string.IsNullOrEmpty(email))
                {
                    // 使用 Gravatar 作為備用
                    var emailHash = System.Security.Cryptography.MD5.Create()
                        .ComputeHash(System.Text.Encoding.UTF8.GetBytes(email.ToLower()))
                        .Select(b => b.ToString("x2"))
                        .Aggregate((a, b) => a + b);
                    ViewBag.UserPicture = $"https://www.gravatar.com/avatar/{emailHash}?s=150&d=identicon";
                }
                else
                {
                    ViewBag.UserPicture = $"https://placehold.co/150x150?text={loginProvider}+User";
                }

                ViewBag.LoginProvider = loginProvider;
                ViewBag.LoginTime = claimsIdentity?.FindFirst("LoginTime")?.Value ?? "未知";
                ViewBag.IsAuthenticated = true;
            }

            return View();
        }

        // POST: Account/Logout
        [HttpPost]
        public ActionResult Logout()
        {
            AuthenticationManager.SignOut("ApplicationCookie");
            return RedirectToAction("Index", "Home");
        }









        // LINE OAuth 輔助方法
        private async Task<LineTokenResponse> ExchangeLineCodeForTokenAsync(string code)
        {
            try
            {
                // 從 Web.config 讀取 LINE 憑證
                var clientId = SocialLoginMvc.SocialAuthConfig.LineChannelId;
                var clientSecret = SocialLoginMvc.SocialAuthConfig.LineChannelSecret;
                var baseUrl = Request.Url.GetLeftPart(UriPartial.Authority);
                if (baseUrl.StartsWith("http://"))
                {
                    baseUrl = baseUrl.Replace("http://", "https://");
                }
                var redirectUri = baseUrl + Request.ApplicationPath.TrimEnd('/') + "/Account/LineCallback";

                var tokenUrl = "https://api.line.me/oauth2/v2.1/token";
                var postData = $"grant_type=authorization_code&" +
                            $"code={code}&" +
                            $"redirect_uri={Uri.EscapeDataString(redirectUri)}&" +
                            $"client_id={clientId}&" +
                            $"client_secret={clientSecret}";

                using (var httpClient = new System.Net.Http.HttpClient())
                {
                    var content = new System.Net.Http.StringContent(postData, System.Text.Encoding.UTF8, "application/x-www-form-urlencoded");
                    var response = await httpClient.PostAsync(tokenUrl, content);
                    var responseString = await response.Content.ReadAsStringAsync()

                    ;

                    System.Diagnostics.Debug.WriteLine($"LINE Token Response: {responseString}");

                    if (!response.IsSuccessStatusCode)
                    {
                        System.Diagnostics.Debug.WriteLine($"LINE Token Error: {response.StatusCode} - {responseString}");
                        return null;
                    }

                    var tokenData = Newtonsoft.Json.JsonConvert.DeserializeObject<LineTokenResponse>(responseString);
                    return tokenData;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ExchangeLineCodeForTokenAsync Error: {ex.Message}");
                return null;
            }
        }

        private Task<LineUserInfo> GetLineUserInfoAsync(string idToken, string nonce)
        {
            try
            {
                // LINE 使用 ID Token (JWT) 來獲取用戶資訊
                // 這裡我們需要解析 JWT Token
                var userInfo = DecodeLineIdToken(idToken, nonce);
                return Task.FromResult(userInfo);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetLineUserInfoAsync Error: {ex.Message}");
                return null;
            }
        }

        private LineUserInfo DecodeLineIdToken(string idToken, string expectedNonce)
        {
            try
            {
                // 簡化的 JWT 解析 (生產環境應該使用專門的 JWT 庫並驗證簽名)
                var parts = idToken.Split('.');
                if (parts.Length != 3)
                {
                    System.Diagnostics.Debug.WriteLine("Invalid JWT format");
                    return null;
                }

                // 解碼 payload (第二部分)
                var payload = parts[1];
                // 添加必要的 padding
                switch (payload.Length % 4)
                {
                    case 2: payload += "=="; break;
                    case 3: payload += "="; break;
                }

                var payloadBytes = Convert.FromBase64String(payload);
                var payloadJson = System.Text.Encoding.UTF8.GetString(payloadBytes);

                System.Diagnostics.Debug.WriteLine($"LINE ID Token Payload: {payloadJson}");

                var tokenData = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(payloadJson);

                // 驗證 nonce (如果提供)
                if (!string.IsNullOrEmpty(expectedNonce) && tokenData.nonce != expectedNonce)
                {
                    System.Diagnostics.Debug.WriteLine($"Nonce mismatch: expected {expectedNonce}, got {tokenData.nonce}");
                    return null;
                }

                return new LineUserInfo
                {
                    Sub = tokenData.sub,
                    Name = tokenData.name,
                    Picture = tokenData.picture
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DecodeLineIdToken Error: {ex.Message}");
                return null;
            }
        }
    }






}
