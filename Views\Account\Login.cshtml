﻿@{
    ViewBag.Title = "登入";
}

<div class="login-container">
    <div class="login-header">
        <h2>🔐 社群登入示範</h2>
        <p>選擇您偏好的社群平台快速登入</p>
    </div>

    <div class="login-body">
        @if (!string.IsNullOrEmpty(ViewBag.Error))
        {
            <div class="alert alert-danger">
                <strong>⚠️ 登入失敗</strong><br />
                @ViewBag.Error
            </div>
        }

        @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
        {
            <div class="alert alert-warning">
                <strong>⚠️ 注意</strong><br />
                @ViewBag.ErrorMessage
            </div>
        }
        <div class="alert alert-info">
            <strong>✨ 使用說明</strong>
            <ul class="mb-0" style="margin-top: 10px;">
                <li>點擊下方按鈕選擇您偏好的登入方式</li>
                <li>您將被重定向到相應的社群平台進行授權</li>
                <li>授權成功後將自動返回並完成登入</li>
            </ul>
        </div>
        <div class="social-buttons">
            <!-- Facebook 登入按鈕 -->
            @using (Html.BeginForm("FacebookLogin", "Account", FormMethod.Post))
            {
                <button type="submit" class="btn btn-block btn-facebook">
                    使用 Facebook 登入
                </button>
            }

            <!-- Google 登入按鈕 -->
            @using (Html.BeginForm("GoogleLogin", "Account", FormMethod.Post))
            {
                <button type="submit" class="btn btn-block btn-google">
                    使用 Google 登入
                </button>
            }

            <!-- LINE 登入按鈕 -->
            @using (Html.BeginForm("LineLogin", "Account", FormMethod.Post))
            {
                <button type="submit" class="btn btn-block btn-line">
                    使用 LINE 登入
                </button>
            }
        </div>

        <hr style="margin: 30px 0;" />

        <div class="text-center">
            <small class="text-muted">
                💡 支援 Facebook、Google、LINE 三種社群登入方式
            </small>
        </div>
    </div>
</div>

@section Scripts {
    <script>

        // 監聽表單提交 (檢查 jQuery 是否可用)
        if (typeof $ !== 'undefined') {
            $('form').on('submit', function(e) {
                var formAction = $(this).attr('action');
                var submitButton = $(this).find('button[type="submit"]').text();

                console.group('📤 表單提交');
                console.log('提交時間:', new Date().toLocaleString());
                console.log('表單動作:', formAction);
                console.log('按鈕文字:', submitButton);
                console.log('表單資料:', $(this).serialize());
                console.groupEnd();
            });
        } else {
            console.warn('jQuery 未載入，跳過表單監聽');
        }
    </script>
}