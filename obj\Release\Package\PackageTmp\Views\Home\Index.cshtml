﻿@{
    ViewBag.Title = "社群登入示範";
}

<div class="jumbotron">
    <h1>🌟 社群登入示範</h1>
    <p class="lead">體驗便捷的第三方社群登入功能，支援 Facebook、Google 和 LINE 三大平台</p>
    <p>@Html.ActionLink("🚀 立即開始", "Login", "Account", null, new { @class = "btn btn-primary btn-lg" })</p>
</div>

<div class="feature-row">
    <div class="feature-col">
        <div class="feature-card">
            <h2>📘 Facebook 登入</h2>
            <p>
                使用全球最大社群平台 Facebook 帳號快速登入，
                享受便利的社群登入體驗。安全取得您的基本資料。
            </p>
            <p>@Html.ActionLink("Facebook 登入", "Login", "Account", null, new { @class = "btn btn-facebook" })</p>
        </div>
    </div>
    <div class="feature-col">
        <div class="feature-card">
            <h2>🔍 Google 登入</h2>
            <p>
                透過 Google 帳號登入，享受 Google 生態系統的
                安全可靠驗證機制，快速完成身份驗證。
            </p>
            <p>@Html.ActionLink("Google 登入", "Login", "Account", null, new { @class = "btn btn-google" })</p>
        </div>
    </div>
    <div class="feature-col">
        <div class="feature-card">
            <h2>💬 LINE 登入</h2>
            <p>
                使用亞洲最受歡迎的通訊軟體 LINE 帳號登入，
                與您的朋友保持連結，享受便利性。
            </p>
            <p>@Html.ActionLink("LINE 登入", "Login", "Account", null, new { @class = "btn btn-line" })</p>
        </div>
    </div>
</div>

<!-- 清除浮動，確保 footer 在正確位置 -->
<div class="clearfix"></div>