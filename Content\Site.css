html, body {
    height: 100%;
}

body {
    padding-top: 70px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
    flex: 1; /* 讓內容區域自動填充剩餘空間 */
    padding-bottom: 50px; /* 給 footer 留出空間 */
}

/* 改善導航列 */
.navbar-inverse {
    background-color: #2c3e50;
    border-color: #2c3e50;
}

.navbar-inverse .navbar-brand {
    color: #ecf0f1;
    font-weight: bold;
}

.navbar-inverse .navbar-nav > li > a {
    color: #bdc3c7;
}

.navbar-inverse .navbar-nav > li > a:hover {
    color: #ecf0f1;
}

/* 響應式導航按鈕 */
.navbar-inverse .navbar-toggle {
    border-color: #ecf0f1;
}

.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
    background-color: #34495e;
}

.navbar-inverse .navbar-toggle .icon-bar {
    background-color: #ecf0f1;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Set width on the form input elements since they're 100% wide by default */
input,
select,
textarea {
    max-width: 280px;
}

/* 登入頁面樣式 */
.login-container {
    max-width: 500px;
    margin: 0 auto;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.login-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 300;
}

.login-body {
    padding: 30px;
}

/* 社群登入按鈕樣式 */
.social-buttons {
    margin-top: 20px;
}

.social-buttons .btn {
    margin-bottom: 15px;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-buttons .btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.social-buttons .btn:hover:before {
    left: 100%;
}

/* 通用社交登入按鈕樣式 */
.btn.btn-facebook,
.btn.btn-google,
.btn.btn-line,
button.btn-facebook,
button.btn-google,
button.btn-line,
a.btn-facebook,
a.btn-google,
a.btn-line {
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    text-transform: none !important;
    letter-spacing: 0.5px !important;
    min-width: 200px !important;
    margin: 8px 0 !important;
    display: inline-block !important;
    /* 調試用：確保 CSS 載入 */
    border: 2px solid transparent !important;
}

/* Facebook 按鈕樣式 */
.btn.btn-facebook {
    background: linear-gradient(135deg, #3b5998 0%, #4c70ba 100%) !important;
    border: none !important;
    color: white !important;
    text-decoration: none !important;
}

.btn.btn-facebook:hover,
.btn.btn-facebook:focus,
.btn.btn-facebook:active {
    background: linear-gradient(135deg, #2d4373 0%, #3b5998 100%) !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 89, 152, 0.3);
}

.btn.btn-facebook:before {
    content: "📘 ";
    margin-right: 8px;
}

/* Google 按鈕樣式 */
.btn.btn-google {
    background: linear-gradient(135deg, #dd4b39 0%, #ea4335 100%) !important;
    border: none !important;
    color: white !important;
    text-decoration: none !important;
}

.btn.btn-google:hover,
.btn.btn-google:focus,
.btn.btn-google:active {
    background: linear-gradient(135deg, #c23321 0%, #dd4b39 100%) !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(221, 75, 57, 0.3);
}

.btn.btn-google:before {
    content: "🔍 ";
    margin-right: 8px;
}

/* LINE 按鈕樣式 */
.btn.btn-line {
    background: linear-gradient(135deg, #00c300 0%, #00b900 100%) !important;
    border: none !important;
    color: white !important;
    text-decoration: none !important;
}

.btn.btn-line:hover,
.btn.btn-line:focus,
.btn.btn-line:active {
    background: linear-gradient(135deg, #009900 0%, #00c300 100%) !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 195, 0, 0.3);
}

.btn.btn-line:before {
    content: "💬 ";
    margin-right: 8px;
}

/* 首頁改善 */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
}

.jumbotron h1 {
    font-weight: 300;
}

/* 功能卡片樣式 */
.feature-card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%; /* 確保所有卡片高度一致 */
    display: flex;
    flex-direction: column;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.feature-card h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.feature-card p {
    flex-grow: 1; /* 讓文字區域自動填充空間 */
    margin-bottom: 20px;
}

/* 確保卡片容器使用 flexbox */
.feature-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.feature-col {
    flex: 1;
    padding: 0 15px;
    margin-bottom: 30px;
}

/* Footer 改善 - Sticky Footer */
footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 20px 0;
    text-align: center;
    margin-top: auto; /* 自動推到底部 */
    flex-shrink: 0; /* 防止 footer 被壓縮 */
}

/* 響應式設計 */
@media (max-width: 768px) {
    .login-container {
        margin: 10px;
        border-radius: 0;
    }

    .login-header {
        padding: 20px;
    }

    .login-body {
        padding: 20px;
    }

    /* 手機版卡片佈局 */
    .feature-row {
        flex-direction: column;
    }

    .feature-col {
        flex: none;
        width: 100%;
        margin-bottom: 20px;
    }

    .feature-card {
        margin-bottom: 20px;
    }

    /* 手機版導航 */
    .navbar-collapse {
        border-top: 1px solid #34495e;
        margin-top: 10px;
        padding-top: 10px;
    }

    .navbar-inverse .navbar-nav > li > a {
        padding: 10px 15px;
    }
}
