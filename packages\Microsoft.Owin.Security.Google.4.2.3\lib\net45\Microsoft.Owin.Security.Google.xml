<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.Google</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware">
            <summary>
            OWIN middleware for authenticating users using Google OAuth 2.0
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware"/>
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="app">The OWIN application</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware.CreateHandler">
            <summary>
            Provides the <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> object for processing authentication-related requests.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> configured with the <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions"/> supplied to the constructor.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions">
            <summary>
            Configuration options for <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.ClientId">
            <summary>
            Gets or sets the Google-assigned client id
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.ClientSecret">
            <summary>
            Gets or sets the Google-assigned client secret
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.AuthorizationEndpoint">
            <summary>
            Gets or sets the URI where the client will be redirected to authenticate.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.TokenEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to exchange the OAuth token.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.UserInformationEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to obtain the user information.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.BackchannelCertificateValidator">
            <summary>
            Gets or sets the a pinned certificate validator to use to validate the endpoints used
            in back channel communications belong to Google.
            </summary>
            <value>
            The pinned certificate validator.
            </value>
            <remarks>If this property is null then the default certificate checks are performed,
            validating the subject name and if the signing chain is a trusted party.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.BackchannelTimeout">
            <summary>
            Gets or sets timeout value in milliseconds for back channel communications with Google.
            </summary>
            <value>
            The back channel timeout in milliseconds.
            </value>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.BackchannelHttpHandler">
            <summary>
            The HttpMessageHandler used to communicate with Google.
            This cannot be set at the same time as BackchannelCertificateValidator unless the value 
            can be downcast to a WebRequestHandler.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.Caption">
            <summary>
            Get or sets the text that the user can display on a sign in user interface.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.CallbackPath">
            <summary>
            The request path within the application's base path where the user-agent will be returned.
            The middleware will process this request when it arrives.
            Default value is "/signin-google".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.SignInAsAuthenticationType">
            <summary>
            Gets or sets the name of another authentication middleware which will be responsible for actually issuing a user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.Provider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider"/> used to handle authentication events.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.StateDataFormat">
            <summary>
            Gets or sets the type used to secure data handled by the middleware.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.Scope">
            <summary>
            A list of permissions to request.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.AccessType">
            <summary>
            access_type. Set to 'offline' to request a refresh token.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions.CookieManager">
            <summary>
            An abstraction for reading and setting cookies during the authentication process.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext">
            <summary>
            Context passed when a Challenge causes a redirect to authorize endpoint in the Google OAuth 2.0 middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions,Microsoft.Owin.Security.AuthenticationProperties,System.String)">
            <summary>
            Creates a new context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The Google OAuth 2.0 middleware options</param>
            <param name="properties">The authenticaiton properties of the challenge</param>
            <param name="redirectUri">The initial redirect URI</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext.RedirectUri">
            <summary>
            Gets the URI used for the redirect operation.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext.Properties">
            <summary>
            Gets the authenticaiton properties of the challenge
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext">
            <summary>
            Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,Newtonsoft.Json.Linq.JObject,System.String,System.String,System.String)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext"/>
            </summary>
            <param name="context">The OWIN environment</param>
            <param name="user">The JSON-serialized Google user info</param>
            <param name="accessToken">Google OAuth 2.0 access token</param>
            <param name="refreshToken">Goolge OAuth 2.0 refresh token</param>
            <param name="expires">Seconds until expiration</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,Newtonsoft.Json.Linq.JObject,Newtonsoft.Json.Linq.JObject)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext"/>
            </summary>
            <param name="context">The OWIN environment</param>
            <param name="user">The JSON-serialized Google user info</param>
            <param name="tokenResponse">The JSON-serialized token response Google</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.User">
            <summary>
            Gets the JSON-serialized user
            </summary>
            <remarks>
            Contains the Google user obtained from the UserInformationEndpoint
            </remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.AccessToken">
            <summary>
            Gets the Google access token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.RefreshToken">
            <summary>
            Gets the Google refresh token
            </summary>
            <remarks>
            This value is not null only when access_type authorize parameter is offline.
            </remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.ExpiresIn">
            <summary>
            Gets the Google access token expiration time
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Id">
            <summary>
            Gets the Google user ID
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Name">
            <summary>
            Gets the user's name
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.GivenName">
            <summary>
            Gets the user's given name
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.FamilyName">
            <summary>
            Gets the user's family name
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Profile">
            <summary>
            Gets the user's profile link
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Email">
            <summary>
            Gets the user's email
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Identity">
            <summary>
            Gets the <see cref="T:System.Security.Claims.ClaimsIdentity"/> representing the user
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.TokenResponse">
            <summary>
            Token response from Google
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext.Properties">
            <summary>
            Gets or sets a property bag for common authentication properties
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider">
            <summary>
            Default <see cref="T:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider"/> implementation.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.#ctor">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.OnAuthenticated">
            <summary>
            Gets or sets the function that is invoked when the Authenticated method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.OnReturnEndpoint">
            <summary>
            Gets or sets the function that is invoked when the ReturnEndpoint method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.OnApplyRedirect">
            <summary>
            Gets or sets the delegate that is invoked when the ApplyRedirect method is invoked.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.Authenticated(Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext)">
            <summary>
            Invoked whenever Google succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Google.GoogleOAuth2ReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context">Contains context information and authentication ticket of the return endpoint.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Google OAuth 2.0 middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.GoogleOAuth2ReturnEndpointContext">
            <summary>
            Provides context information to middleware providers.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.GoogleOAuth2ReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Initialize a <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2ReturnEndpointContext"/>
            </summary>
            <param name="context">OWIN environment</param>
            <param name="ticket">The authentication ticket</param>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider">
            <summary>
            Specifies callback methods which the <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. />
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider.Authenticated(Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticatedContext)">
            <summary>
            Invoked whenever Google succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Google.GoogleOAuth2ReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context">Contains context information and authentication ticket of the return endpoint.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Google.IGoogleOAuth2AuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Google.GoogleOAuth2ApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Google OAuth 2.0 middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="T:Microsoft.Owin.Security.Google.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.Resources.Exception_OptionMustBeProvided">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; option must be provided..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Google.Resources.Exception_ValidatorHandlerMismatch">
            <summary>
              Looks up a localized string similar to An ICertificateValidator cannot be specified at the same time as an HttpMessageHandler unless it is a WebRequestHandler..
            </summary>
        </member>
        <member name="T:Owin.GoogleAuthenticationExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Owin.GoogleAuthenticationExtensions.UseGoogleAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Google.GoogleOAuth2AuthenticationOptions)">
            <summary>
            Authenticate users using Google OAuth 2.0
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="options">Middleware configuration options</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="M:Owin.GoogleAuthenticationExtensions.UseGoogleAuthentication(Owin.IAppBuilder,System.String,System.String)">
            <summary>
            Authenticate users using Google OAuth 2.0
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="clientId">The google assigned client id</param>
            <param name="clientSecret">The google assigned client secret</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
    </members>
</doc>
