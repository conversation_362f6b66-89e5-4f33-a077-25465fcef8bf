﻿@{
    ViewBag.Title = "個人資料";
}

<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <h2>@ViewBag.Title</h2>
        <hr />

        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                @TempData["SuccessMessage"]
            </div>
        }

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">個人資訊</h3>
            </div>
            <div class="panel-body">
                @if (ViewBag.IsAuthenticated)
                {
                    <div class="alert alert-success">
                        <strong>登入成功！</strong> 使用 Microsoft OWIN 成功獲取用戶資訊。
                    </div>
                }
                
                <div class="row">
                    <div class="col-md-3">
                        <img src="@ViewBag.UserPicture"
                             class="img-thumbnail"
                             alt="用戶頭像"
                             style="width: 150px; height: 150px; object-fit: cover;"
                             onerror="this.src='https://placehold.co/150x150?text=User'" />
                    </div>
                    <div class="col-md-9">
                        <table class="table table-striped">
                            <tr>
                                <td><strong>姓名：</strong></td>
                                <td>@ViewBag.UserName</td>
                            </tr>
                            <tr>
                                <td><strong>電子郵件：</strong></td>
                                <td>@ViewBag.Email</td>
                            </tr>
                            <tr>
                                <td><strong>登入方式：</strong></td>
                                <td>@ViewBag.LoginProvider</td>
                            </tr>
                            <tr>
                                <td><strong>登入時間：</strong></td>
                                <td>@ViewBag.LoginTime</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <hr />
                
                <div class="text-center">
                    @if (ViewBag.IsAuthenticated)
                    {
                        using (Html.BeginForm("Logout", "Account", FormMethod.Post))
                        {
                            <button type="submit" class="btn btn-warning">登出</button>
                        }
                    }
                    else
                    {
                        @Html.ActionLink("前往登入", "Login", "Account", null, new { @class = "btn btn-primary" })
                    }
                    @Html.ActionLink("返回首頁", "Index", "Home", null, new { @class = "btn btn-default" })
                </div>
            </div>
        </div>
    </div>
</div>